import formData from 'form-data';
import Mailgun from 'mailgun.js';

interface FormData {
    missionStatus: string;
    activitySector: string;
    billingMethod: string;
    customRate: string;
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
}

const mailgun = new Mailgun(formData);

// Ensure API key is available
if (!process.env.MAILGUN_API_KEY) {
    throw new Error('Mailgun API key is not configured');
}

if (!process.env.MAILGUN_DOMAIN) {
    throw new Error('Mailgun domain is not configured');
}

if (!process.env.MAILGUN_FROM_EMAIL) {
    throw new Error('Mailgun from email is not configured');
}

const mg = mailgun.client({
    username: 'api',
    key: process.env.MAILGUN_API_KEY,
    url: 'https://api.eu.mailgun.net', // EU region endpoint
    timeout: 30000, // Increased timeout
});

export const sendEmail = async (data: FormData) => {
    const missionStatusMap: { [key: string]: string } = {
        "found": "J'ai déjà trouvé une mission",
        "negotiating": "J'ai une mission en cours de négociation",
        "searching": "Je n'ai pas encore de mission"
    };

    const messageData = {
        from: `Unitportage Form <${process.env.MAILGUN_FROM_EMAIL}>`,
        to: '<EMAIL>',
        subject: 'Nouvelle soumission de formulaire',
        text: `
Nouvelle soumission du formulaire:

Statut de la mission: ${missionStatusMap[data.missionStatus] || data.missionStatus}
Secteur d'activité: ${data.activitySector}
Mode de facturation: ${data.billingMethod}
Tarif: ${data.customRate}

Informations du contact:
Nom: ${data.lastName}
Prénom: ${data.firstName}
Email: ${data.email}
Téléphone: ${data.phone}
`,
        html: `
<h2>Nouvelle soumission du formulaire</h2>
<h3>Informations de la mission:</h3>
<ul>
    <li><strong>Statut de la mission:</strong> ${missionStatusMap[data.missionStatus] || data.missionStatus}</li>
    <li><strong>Secteur d'activité:</strong> ${data.activitySector}</li>
    <li><strong>Mode de facturation:</strong> ${data.billingMethod}</li>
    <li><strong>Tarif:</strong> ${data.customRate}</li>
</ul>
<h3>Informations du contact:</h3>
<ul>
    <li><strong>Nom:</strong> ${data.lastName}</li>
    <li><strong>Prénom:</strong> ${data.firstName}</li>
    <li><strong>Email:</strong> ${data.email}</li>
    <li><strong>Téléphone:</strong> ${data.phone}</li>
</ul>
`
    };

    try {
        const result = await mg.messages.create(process.env.MAILGUN_DOMAIN!, messageData);
        return result;
    } catch (error: any) {
        console.error('Failed to send email:', error);
        throw new Error(error.message || 'Failed to send email');
    }
};