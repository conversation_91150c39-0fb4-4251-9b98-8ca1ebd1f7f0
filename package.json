{"name": "form", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.6.0", "form-data": "^4.0.3", "lucide-react": "^0.511.0", "mailgun.js": "^12.0.2", "next": "15.3.2", "react": "^18.3.1", "react-calendly": "^4.4.0", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "tailwind-merge": "^3.3.0", "zod": "^3.25.56"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/form-data": "^2.2.1", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "tailwindcss": "^4", "tw-animate-css": "^1.3.0", "typescript": "^5"}}