import React from 'react';

const styles = {
  ImageContainer: {
    top: '252px',
    left: '348px',
    width: '348px',
    height: '348px',
    borderRadius: '8px',
    backgroundImage: 'url(./image.png)',
    backgroundPosition: 'center center',
    backgroundSize: 'cover',
    backgroundRepeat: 'no-repeat',
  },
};

const defaultProps = {
  image: 'https://assets.api.uizard.io/api/cdn/stream/e6cda47f-4e65-4525-ad14-4d4f0befa467.png',
}

const Bgimg = (props) => {
  return (
    <div style={{
      ...styles.ImageContainer,
      backgroundImage: `url(${props.image ?? defaultProps.image})`,
    }} />
  );
};

export default Bgimg;