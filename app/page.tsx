"use client"

import { useState, useRef, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Calendar } from "@/components/ui/calendar"
import { Users, MessageSquare, Search, User, Mail, Phone, Euro, ShieldCheck, Gift, ChevronLeft, Clock } from "lucide-react"
import Image from "next/image"
import Bgimg from "@/components/uizard/Bgimg"
import { InlineWidget } from "react-calendly";
import emailjs from '@emailjs/browser';

export default function MultiStepForm() {
  const [currentStep, setCurrentStep] = useState(1)
  const [formData, setFormData] = useState({
    missionStatus: "",
    activitySector: "",
    billingMethod: "TJM",
    customRate: "",
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    selectedDate: new Date(),
    selectedTime: "",
  })
  const [errors, setErrors] = useState({
    missionStatus: false,
    activitySector: false,
    billingMethod: false,
    customRate: false,
    firstName: false,
    lastName: false,
    email: false,
    phone: false
  })
  const [hover, setHover] = useState<string | null>(null)

  const customRateInputRef = useRef<HTMLInputElement>(null)
  const lasttNameInputRef = useRef<HTMLInputElement>(null)
  const activitySectorRef = useRef<HTMLButtonElement>(null)

  // Validate first step fields
  const validateStep1 = () => {
    const newErrors = {
      missionStatus: !formData.missionStatus,
      activitySector: !formData.activitySector,
      billingMethod: !formData.billingMethod,
      customRate: false
    }

    // Custom rate is required regardless of billing method
    if (!formData.customRate) {
      newErrors.customRate = true
    }

    setErrors({ ...errors, ...newErrors })

    // Return true if no errors
    return !Object.values(newErrors).some(error => error)
  }

  // Handle billing method selection
  const handleBillingMethodChange = (method: string) => {
    setFormData({ ...formData, billingMethod: method })
  }

  // Handle custom rate input change
  const handleCustomRateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    // Don't change the billing method when typing in the custom field
    setFormData({ ...formData, customRate: value })
    if (value) {
      setErrors({ ...errors, customRate: false })
    }
  }

  // Focus custom rate input when billing method is "custom"
  useEffect(() => {
    if (formData.billingMethod === "custom" && customRateInputRef.current) {
      customRateInputRef.current.focus()
    }
  }, [formData.billingMethod])

  // Validate second step fields
  const validateStep2 = () => {
    const newErrors = {
      ...errors,
      firstName: !formData.firstName,
      lastName: !formData.lastName,
      email: !formData.email,
      phone: !formData.phone
    }

    // Email validation
    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = true
    }

    // Phone validation (simple format check)
    if (formData.phone && !/^[0-9\s+()-]{10,15}$/.test(formData.phone)) {
      newErrors.phone = true
    }

    setErrors(newErrors)

    // Return true if no errors in step 2 fields
    return !Object.values({
      firstName: newErrors.firstName,
      lastName: newErrors.lastName,
      email: newErrors.email,
      phone: newErrors.phone
    }).some(error => error)
  }

  const sendEmail = async (data: any) => {
    try {
      const missionStatusMap: { [key: string]: string } = {
        found: "J'ai déjà trouvé une mission",
        negotiating: "J'ai une mission en cours de négociation",
        searching: "Je n'ai pas encore de mission"
      };

      await emailjs.send(
        'service_d7hvwtn',
        'template_hglagln',
        {
          // Les clés ici correspondent exactement aux {{…}} du template HTML
          mission_status: missionStatusMap[data.missionStatus] || data.missionStatus,
          activity_sector: data.activitySector,
          billing_method:
            data.billingMethod === "TJM"
              ? "TJM"
              : data.billingMethod === "forfait"
                ? "Forfait"
                : data.billingMethod,
          custom_rate: data.customRate,
          first_name: data.firstName,
          last_name: data.lastName,
          email: data.email,
          phone: data.phone
        },
        'VkqYm7ycV0ALodaSU'
      );
      console.log('Email envoyé avec succès');
    } catch (error) {
      console.error('Échec de l\'envoi de l\'email:', error);
    }
  };

  const handleNext = () => {
    if (currentStep === 1) {
      const isValid = validateStep1()
      if (isValid) {
        setCurrentStep(currentStep + 1)
      }
    } else if (currentStep < 3) {
      setCurrentStep(currentStep + 1)
    }
  }

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleSubmit = () => {
    const isValid = validateStep2()
    if (isValid) {
      setCurrentStep(3)
      sendEmail(formData)
    }
  }

  const handleReturnHome = () => {
    // Reset form state
    // setCurrentStep(1)
    setFormData({
      missionStatus: "",
      activitySector: "",
      billingMethod: "TJM",
      customRate: "",
      firstName: "",
      lastName: "",
      email: "",
      phone: "",
      selectedDate: new Date(),
      selectedTime: "",
    })
    // Redirect to the specified URL
    window.location.href = 'https://uni.my-dev.tech/'
  }

  // Log form data when user reaches step 3, excluding selectedDate initially
  useEffect(() => {
    if (currentStep === 3) {
      const { selectedDate, ...dataWithoutDate } = formData;
      console.log('Form submission data:', dataWithoutDate);
    }
  }, [currentStep, formData.missionStatus, formData.activitySector,
    formData.billingMethod, formData.customRate, formData.firstName,
    formData.lastName, formData.email, formData.phone]);

  // Log all data including date when time is selected
  useEffect(() => {
    if (currentStep === 3 && formData.selectedTime) {
      console.log('Complete form data with date and time:', formData);
    }
  }, [currentStep, formData.selectedTime]);

  // Add this useEffect for scroll and focus behavior
  useEffect(() => {
    if (currentStep === 2) {
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      })
      const timer = setTimeout(() => {
        lasttNameInputRef.current?.focus()
      }, 500) // Increased delay for smoother transition
      return () => clearTimeout(timer)
    }
  }, [currentStep])

  // Smoother scroll for activity sector
  useEffect(() => {
    if (formData.missionStatus) {
      const timer = setTimeout(() => {
        activitySectorRef.current?.scrollIntoView({
          behavior: 'smooth',
          block: 'center',
          inline: 'nearest'
        })
        const focusTimer = setTimeout(() => {
          activitySectorRef.current?.focus()
        }, 300) // Separate focus timing for smoother effect
        return () => clearTimeout(focusTimer)
      }, 100)
      return () => clearTimeout(timer)
    }
  }, [formData.missionStatus])


  return (
    <div className="flex flex-col min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <div className="flex items-center justify-center">

            <Image src="/logo.svg" alt="Logo" width={150} height={50} />

          </div>
        </div>
      </div>



      {/* Main Content - Added flex-grow to take available space */}
      <div className="flex-grow flex items-start justify-center">
        <div className="max-w-4xl w-full mx-auto px-4 py-8">

          {/* Progress Indicator */}
          {currentStep < 3 && (
            <div className="w-[80%] md:w-60 pb-8 mx-auto ">


              <div className="max-w-2xl flex flex-row justify-around mx-auto pb-2 ">
                {/* <span className={`ml-2 text-sm ${currentStep == 2 ? "text-gray-800 font-medium" : "text-gray-400"}`}> */}
                <span className="text-gray-400">
                  Situation
                </span>
                {/* <span className={`ml-2 text-sm ${currentStep != 2 ? "text-gray-800 font-medium" : "text-gray-400"}`}> */}
                <span className="text-gray-400">
                  Coordonnées
                </span>
              </div>


              <div className="relative w-full h-1 bg-gray-200 rounded-full mx-auto">

                <div className={`absolute top-0 left-0 h-1 ${currentStep == 1 ? " bg-gray-200" : "bg-[#020033]"} rounded-full transition-all duration-300`} style={{ width: '50%' }} > </div>

                <div className={`absolute top-1/2 w-4 h-4 rounded-full flex items-center justify-center transition-all duration-300 z-10`} style={{ left: '50%', transform: 'translate(-50%, -50%)' }}  >

                  <div className={`w-full h-full rounded-full ${currentStep == 1 ? " bg-white" : "bg-[#020033]"} border-2 border-gray-400 flex items-center justify-center`}>

                    {currentStep == 1 ? null :
                      <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="3" d="M5 13l4 4L19 7"></path>
                      </svg>
                    }

                  </div>

                </div>

                <div className={`absolute top-1/2 w-4 h-4 rounded-full border-2 border-gray-400 bg-white transition-all duration-300 z-10`} style={{ left: '100%', transform: 'translate(-50%, -50%)' }} ></div>

              </div>

            </div>
          )}



          {/* Step 1: Situation */}
          {currentStep === 1 && (
            <div className="space-y-8">
              <div className="text-center">
                <h1 className="text-2xl md:text-3xl font-bold text-gray-800 mb-2">Votre situation</h1>
              </div>

              <div className="max-w-2xl mx-auto space-y-8">
                {/* Mission Status */}
                <div>
                  <h2 className="text-lg font-semibold text-gray-800 mb-4">Avez-vous déjà trouvé une mission ?</h2>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Card
                      className={`cursor-pointer transition-all ${formData.missionStatus === "trouvé" ? "bg-[#020033] text-white" : "hover:bg-[#020033] hover:text-white"} ${errors.missionStatus ? 'border-red-500' : ''}`}
                      onClick={() => {
                        setFormData({ ...formData, missionStatus: "trouvé" })
                        setErrors({ ...errors, missionStatus: false })
                      }}
                      onMouseEnter={() => setHover("trouvé")}
                      onMouseLeave={() => setHover(null)}
                    >
                      <CardContent className="p-6 text-center flex flex-col items-center">


                        {formData.missionStatus === "trouvé" || hover === "trouvé" ? (
                          <Image className="mb-4" src={"/negotiation_dark.svg"} alt="My Profile" width={80} height={80} />
                        ) : (
                          <Image className="mb-4" src={"/negotiation_light.svg"} alt="My Profile" width={80} height={80} />
                        )}
                        <p className="text-sm font-medium">J'ai déjà trouvé</p>
                        <p className="text-sm font-medium">une mission</p>
                      </CardContent>
                    </Card>
                    <Card
                      className={`cursor-pointer transition-all ${formData.missionStatus === "négocion" ? "bg-[#020033] text-white" : "hover:bg-[#020033] hover:text-white"} ${errors.missionStatus ? 'border-red-500' : ''}`}
                      onClick={() => {
                        setFormData({ ...formData, missionStatus: "négocion" })
                        setErrors({ ...errors, missionStatus: false })
                      }}
                      onMouseEnter={() => setHover("négocion")}
                      onMouseLeave={() => setHover(null)}
                    >
                      <CardContent className="p-6 text-center flex flex-col items-center">
                        {formData.missionStatus === "négocion" || hover === "négocion" ? (
                          <Image className="mb-4" src={"/negot_dark.svg"} alt="My Profile" width={80} height={80} />
                        ) : (
                          <Image className="mb-4" src={"/negot_light.svg"} alt="My Profile" width={80} height={80} />
                        )}
                        <p className="text-sm font-medium">J'ai une mission en</p>
                        <p className="text-sm font-medium">cours de négociation</p>
                      </CardContent>
                    </Card>
                    <Card
                      className={`cursor-pointer transition-all ${formData.missionStatus === "recherche" ? "bg-[#020033] text-white" : "hover:bg-[#020033] hover:text-white"} ${errors.missionStatus ? 'border-red-500' : ''}`}
                      onClick={() => {
                        setFormData({ ...formData, missionStatus: "recherche" })
                        setErrors({ ...errors, missionStatus: false })
                      }}
                      onMouseEnter={() => setHover("recherche")}
                      onMouseLeave={() => setHover(null)}
                    >
                      <CardContent className="p-6 text-center flex flex-col items-center">
                        {formData.missionStatus === "recherche" || hover === "recherche" ? (
                          <Image className="mb-4" src={"/loupe_dark.svg"} alt="My Profile" width={80} height={80} />
                        ) : (
                          <Image className="mb-4" src={"/loupe_light.svg"} alt="My Profile" width={80} height={80} />
                        )}
                        <p className="text-sm font-medium">Je n'ai pas encore</p>
                        <p className="text-sm font-medium">de mission</p>
                      </CardContent>
                    </Card>
                  </div>
                  {errors.missionStatus && (
                    <p className="text-red-500 text-sm mt-1">Veuillez sélectionner votre situation</p>
                  )}
                </div>

                {/* Activity Sector - Only show if mission status is selected */}
                {formData.missionStatus && (
                  <>
                    <div>
                      <Label htmlFor="sector" className="text-lg font-semibold text-gray-800">
                        Quel est votre secteur d'activité ?
                      </Label>
                      <Select
                        value={formData.activitySector}
                        onValueChange={(value) => {
                          setFormData({ ...formData, activitySector: value })
                          setErrors({ ...errors, activitySector: false })
                        }}
                      >
                        <SelectTrigger
                          ref={activitySectorRef}
                          className={`mt-2 w-full bg-white ${errors.activitySector ? 'border-red-500 ring-red-200' : ''}`}
                        >
                          <SelectValue placeholder="Sélectionnez votre secteur d'activité" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="achats">Achats</SelectItem>
                          <SelectItem value="batiment">Bâtiment - BTP - Construction</SelectItem>
                          <SelectItem value="coaching">Coaching</SelectItem>
                          <SelectItem value="communication">Communication</SelectItem>
                          <SelectItem value="conseil">Consultant confirmé - Conseil stratégique</SelectItem>
                          <SelectItem value="dev-commercial">Développement commercial</SelectItem>
                          <SelectItem value="dev-informatique">Développement informatique</SelectItem>
                          <SelectItem value="management">Dirigeant - Management de transition</SelectItem>
                          <SelectItem value="etudes">Études - Recherche et développement</SelectItem>
                          <SelectItem value="formation">Formation - Enseignement</SelectItem>
                          <SelectItem value="finance">Gestion - Finance</SelectItem>
                          <SelectItem value="design">Graphisme - Design - Création</SelectItem>
                          <SelectItem value="immobilier">Immobilier</SelectItem>
                          <SelectItem value="it">Informatique - Systèmes d'information</SelectItem>
                          <SelectItem value="ingenierie">Ingénierie technique</SelectItem>
                          <SelectItem value="logistique">Logistique - Supply chain</SelectItem>
                          <SelectItem value="marketing">Marketing</SelectItem>
                          <SelectItem value="qualite">Qualité - Sécurité - Environnement -Réglementation</SelectItem>
                          <SelectItem value="rh">Ressources Humaines - Conseil en management</SelectItem>
                          <SelectItem value="web">Web - Digital - Internet</SelectItem>
                          <SelectItem value="autre">Autre</SelectItem>
                        </SelectContent>
                      </Select>
                      {errors.activitySector && (
                        <p className="text-red-500 text-sm mt-1">Veuillez sélectionner un secteur d'activité</p>
                      )}
                    </div>

                    {/* Billing Method */}
                    <div>
                      <h2 className="text-lg font-semibold text-gray-800 mb-4">Quel est votre mode de facturation ?</h2>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <Button
                          variant={formData.billingMethod === "TJM" ? "default" : "outline"}
                          className={`h-12 ${formData.billingMethod === "TJM" ? "bg-[#020033] hover:bg-[#020033]/90 text-white" : ""} ${errors.billingMethod ? 'border-red-500' : ''}`}
                          onClick={() => {
                            handleBillingMethodChange("TJM")
                            setErrors({ ...errors, billingMethod: false })
                          }}
                        >
                          TJM
                        </Button>
                        <Button
                          variant={formData.billingMethod === "forfait" ? "default" : "outline"}
                          className={`h-12 ${formData.billingMethod === "forfait" ? "bg-[#020033] hover:bg-[#020033]/90 text-white" : ""} ${errors.billingMethod ? 'border-red-500' : ''}`}
                          onClick={() => {
                            handleBillingMethodChange("forfait")
                            setErrors({ ...errors, billingMethod: false })
                          }}
                        >
                          Forfait
                        </Button>
                        <Input
                          ref={customRateInputRef}
                          placeholder="Saisir votre tarif"
                          value={formData.customRate}
                          onChange={(e) => {
                            handleCustomRateChange(e)
                            setErrors({ ...errors, customRate: false })
                          }}
                          className={`h-12 bg-white ${errors.customRate ? 'border-red-500 ring-red-200' : ''}`}
                        />
                      </div>
                      {errors.billingMethod && (
                        <p className="text-red-500 text-sm mt-1">Veuillez sélectionner un mode de facturation</p>
                      )}
                      {errors.customRate && (
                        <p className="text-red-500 text-sm mt-1">Veuillez saisir votre tarif</p>
                      )}
                    </div>

                    <div className="flex justify-end">
                      <Button
                        onClick={handleNext}
                        className="bg-orange-500 hover:bg-orange-600 text-white px-8 py-6 text-lg rounded-full"
                      >
                        Continuer
                      </Button>
                    </div>
                  </>
                )}
              </div>
            </div>
          )}

          {/* Step 2: Contact Details */}
          {currentStep === 2 && (
            <div className="space-y-8">
              <div className="text-center">
                <h1 className="text-2xl md:text-3xl font-bold text-[#020033] mb-2">Vos coordonnées</h1>
              </div>

              <div className="max-w-2xl mx-auto space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="lastName" className="text-sm font-medium text-gray-700">
                      Votre nom
                    </Label>
                    <div className="relative mt-1">
                      <User className="absolute left-3 top-4 h-4 w-4 text-gray-400" />
                      <Input
                        id="lastName"
                        ref={lasttNameInputRef}
                        placeholder="Durand"
                        value={formData.lastName}
                        onChange={(e) => {
                          setFormData({ ...formData, lastName: e.target.value })
                          if (e.target.value) setErrors({ ...errors, lastName: false })
                        }}
                        className={`h-[50px] pl-10 bg-white ${errors.lastName ? 'border-red-500 ring-red-200' : ''}`}
                      />
                      {errors.lastName && (
                        <p className="text-red-500 text-sm mt-1">Veuillez saisir votre nom</p>
                      )}
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="firstName" className="text-sm font-medium text-gray-700">
                      Votre prénom
                    </Label>
                    <div className="relative mt-1">
                      <User className="absolute left-3 top-4 h-4 w-4 text-gray-400" />
                      <Input
                        id="firstName"
                        placeholder="Jean"
                        value={formData.firstName}
                        onChange={(e) => {
                          setFormData({ ...formData, firstName: e.target.value })
                          if (e.target.value) setErrors({ ...errors, firstName: false })
                        }}
                        className={`h-[50px] pl-10 bg-white ${errors.firstName ? 'border-red-500 ring-red-200' : ''}`}
                      />
                      {errors.firstName && (
                        <p className="text-red-500 text-sm mt-1">Veuillez saisir votre prénom</p>
                      )}
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="email" className="text-sm font-medium text-gray-700">
                      Adresse email
                    </Label>
                    <div className="relative mt-1">
                      <Mail className="absolute left-3 top-4 h-4 w-4 text-gray-400" />
                      <Input
                        id="email"
                        type="email"
                        placeholder="<EMAIL>"
                        value={formData.email}
                        onChange={(e) => {
                          setFormData({ ...formData, email: e.target.value })
                          if (e.target.value && /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e.target.value)) {
                            setErrors({ ...errors, email: false })
                          }
                        }}
                        className={`h-[50px] pl-10 bg-white ${errors.email ? 'border-red-500 ring-red-200' : ''}`}
                      />
                      {errors.email && (
                        <p className="text-red-500 text-sm mt-1">Veuillez saisir une adresse email valide</p>
                      )}
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="phone" className="text-sm font-medium text-gray-700">
                      Numéro de téléphone
                    </Label>
                    <div className="relative mt-1">
                      <Phone className="absolute left-3 top-4 h-4 w-4 text-gray-400" />
                      <Input
                        id="phone"
                        placeholder="06 00 00 00 00"
                        value={formData.phone}
                        onChange={(e) => {
                          setFormData({ ...formData, phone: e.target.value })
                          if (e.target.value && /^[0-9\s+()-]{10,15}$/.test(e.target.value)) {
                            setErrors({ ...errors, phone: false })
                          }
                        }}
                        className={`h-[50px] pl-10 bg-white ${errors.phone ? 'border-red-500 ring-red-200' : ''}`}
                      />
                      {errors.phone && (
                        <p className="text-red-500 text-sm mt-1">Veuillez saisir un numéro de téléphone valide</p>
                      )}
                    </div>
                  </div>
                </div>

                <div className="flex flex-col justify-between items-start pt-4">

                  <Button
                    onClick={handleSubmit}
                    className="bg-orange-500 hover:bg-orange-600 text-white px-8 py-6 text-lg w-full rounded-full"
                  >
                    Valider
                  </Button>

                  <Button variant="link" onClick={handleBack} className="text-gray-600  mt-6">
                    <ChevronLeft className="w-4 h-4 mr-1" />
                    Retour
                  </Button>
                </div>
              </div>
            </div>
          )}

          {/* Step 3: Confirmation */}
          {currentStep === 3 && (
            <div className="space-y-8">
              <div className="text-center space-y-4">
                <h1 className="text-2xl md:text-3xl font-bold text-[#020033]">Votre demande a bien été envoyée !</h1>
                <p className="text-gray-600 max-w-md mx-auto">
                  Merci pour votre confiance, un conseiller va vous contacter très prochainement.
                </p>
              </div>

              <div className="max-w-6xl mx-auto relative">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {/* Left side - Illustration and Return Button */}
                  <div className="flex flex-col items-start ">
                    <div className="w-full flex flex-col items-center ">
                      <Bgimg />
                      <Button
                        onClick={handleReturnHome}
                        className="bg-orange-500 hover:bg-orange-600 text-white w-full max-w-xs mx-auto flex justify-center items-center py-6 mt-8 rounded-full"
                      >
                        <span className="text-xl font-medium py-3 text-center  ">Retour à l'accueil</span>
                      </Button>
                    </div>
                  </div>

                  {/* Divider for large screens */}
                  <div
                    className="hidden lg:block absolute left-1/2 rounded transform -translate-x-1/2 w-[2px] bg-gray-200"
                    style={{ height: '100%', top: '0%' }}
                  ></div>

                  {/* Right side - Appointment Booking */}
                  <div className="flex flex-col">
                    {!formData.selectedTime ? (
                      <>
                        <div className="text-center mb-6">
                          <h2 className="text-xl font-semibold text-[#020033] mb-2">Besoin d'une réponse rapide ?</h2>
                          <p className="text-gray-600">
                            Prenez rendez-vous avec
                            <br />
                            un conseiller dès maintenant.
                          </p>
                        </div>

                        <div className="rounded-lg  h-[850px]">
                          <InlineWidget
                            url="https://calendly.com/l-dasilva-unitportage/demande-de-reponse-rapide"
                            styles={{
                              height: '100%',
                              width: '100%',
                            }}
                            prefill={{
                              email: formData.email,
                              name: `${formData.firstName} ${formData.lastName}`,
                              firstName: formData.firstName,
                              lastName: formData.lastName,
                            }}
                          />
                        </div>
                      </>
                    ) : (
                      <div className="text-center space-y-6 py-8">
                        <h2 className="text-xl font-semibold text-green-600">Rendez-vous confirmé !</h2>
                        <p className="text-gray-700">
                          Merci d'avoir pris rendez-vous avec un conseiller.
                          <br />
                          Vous recevrez bientôt une confirmation par email.
                        </p>
                      </div>
                    )}
                  </div>
                </div>

              </div>
            </div>
          )}
        </div>
      </div>

      {/* Footer Features - Now will stick to bottom */}
      <div className="border-t bg-white mt-auto">
        <div className="max-w-lg mx-auto px-4 py-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
            <div className="space-y-0">

              <Image src={"/euro.svg"} alt="" width={80} height={80} className="mx-auto mb-4" />
              <h3 className="font-semibold text-gray-800">Financièrement</h3>
              <h3 className="font-semibold text-gray-800">avantageux</h3>
            </div>
            <div className="space-y-0">

              <Image src={"/shield.svg"} alt="" width={80} height={80} className="mx-auto mb-4" />
              <h3 className="font-semibold text-gray-800">Une sécurité</h3>
              <h3 className="font-semibold text-gray-800">complète</h3>
            </div>
            <div className="space-y-0">

              <Image src={"/gift.svg"} alt="" width={80} height={80} className="mx-auto mb-4" />
              <h3 className="font-semibold text-gray-800">Et bien plus</h3>
              <h3 className="font-semibold text-gray-800">encore !</h3>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
