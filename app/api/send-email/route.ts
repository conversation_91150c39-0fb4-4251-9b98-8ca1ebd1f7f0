import { NextRequest, NextResponse } from 'next/server';
import { sendEmail } from '@/lib/mailgun';
import { z } from 'zod';

// Define form data validation schema
const formSchema = z.object({
    missionStatus: z.string().min(1, "Statut de mission est requis"),
    activitySector: z.string().min(1, "Secteur d'activité est requis"),
    billingMethod: z.string().min(1, "Mode de facturation est requis"),
    customRate: z.string().min(1, "Tarif est requis"),
    firstName: z.string().min(1, "Prénom est requis"),
    lastName: z.string().min(1, "Nom est requis"),
    email: z.string().email("Email invalide"),
    phone: z.string().regex(/^[0-9\s+()-]{10,15}$/, "Numéro de téléphone invalide"),
    selectedDate: z.any().optional(),
    selectedTime: z.string().optional()
});

export async function POST(req: NextRequest) {
    try {
        // Log environment variables (without sensitive data)
        console.log('Checking environment:', {
            hasApiKey: !!process.env.MAILGUN_API_KEY,
            domain: process.env.MAILGUN_DOMAIN,
            fromEmail: process.env.MAILGUN_FROM_EMAIL
        });

        // Parse and validate request body
        const body = await req.json();
        console.log('Received form data:', {
            ...body,
            email: '***@***.***' // Hide actual email for privacy
        });

        const validatedData = formSchema.parse(body);

        // Send email using the shared Mailgun utility
        const result = await sendEmail(validatedData);

        return NextResponse.json({ success: true, result });
    } catch (error) {
        console.error('Error processing form submission:', error);

        // Handle validation errors
        if (error instanceof z.ZodError) {
            return NextResponse.json(
                {
                    error: "Validation failed",
                    details: error.errors.map((err: z.ZodError) => ({
                        field: err.path.join('.'),
                        message: err.message
                    }))
                },
                { status: 400 }
            );
        }

        // Handle other errors
        const errorMessage = error instanceof Error ? error.message : "Internal server error";
        return NextResponse.json(
            { error: errorMessage },
            { status: 500 }
        );
    }
}
